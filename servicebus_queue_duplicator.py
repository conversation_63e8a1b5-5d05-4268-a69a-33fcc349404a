#!/usr/bin/env python3
"""
Azure Service Bus Queue Message Duplicator

This script duplicates messages from source Service Bus queues to destination Service Bus queues.
It handles both active messages and dead letter queue messages while preserving message properties.

Requirements:
- azure-servicebus>=7.11.0
- Python 3.7+

Environment Variables:
- SOURCE_SB_CONNECTION_STRING: Source Service Bus connection string
- DEST_SB_CONNECTION_STRING: Destination Service Bus connection string
"""

from azure.servicebus import ServiceBusClient, ServiceBusMessage, ServiceBusReceiveMode
import os
import logging
import time
import datetime
from typing import List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
SOURCE_CONNECTION_STR = os.getenv("SOURCE_SB_CONNECTION_STRING")
DEST_CONNECTION_STR = os.getenv("DEST_SB_CONNECTION_STRING")

# Validate environment variables
if not SOURCE_CONNECTION_STR:
    raise ValueError("SOURCE_SB_CONNECTION_STRING environment variable is required")
if not DEST_CONNECTION_STR:
    raise ValueError("DEST_SB_CONNECTION_STRING environment variable is required")

# List of queues to migrate
QUEUES = [
    "error-subscription-audit-aks-dev",
    # Uncomment and add more queues as needed:
    # "error-subscription-audit-aks-training",
    # "error-subscription-audit-aks-usr",
    # "queue-cad-training",
    # "queue-cad-usr",
    # "subscription-audit-aks-training",
    # "subscription-audit-aks-usr"
]

# You can also load queues from a file
def load_queues_from_file(filename: str) -> List[str]:
    """Load queue names from a text file (one per line)"""
    try:
        with open(filename, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        logger.warning(f"Queue file '{filename}' not found. Using default queue list.")
        return QUEUES

# Configuration options
BATCH_SIZE = 10  # Number of messages to process in each batch
MAX_RETRIES = 3  # Maximum number of retries for failed operations
RETRY_DELAY = 1  # Delay between retries in seconds


def extract_message_body(original_msg):
    """
    Extract the actual message body content from a ServiceBusReceivedMessage.
    Handles different body types including generators.
    """
    try:
        # Method 1: Try to get body directly if it's already the right type
        if hasattr(original_msg, 'body'):
            body = original_msg.body

            # If body is string or bytes, return as-is
            if isinstance(body, (str, bytes)):
                return body

            # If body is None, return None
            if body is None:
                return None

            # If body is a generator/iterator, consume it
            if hasattr(body, '__iter__'):
                try:
                    # Try to join as bytes
                    body_parts = list(body)
                    if body_parts and isinstance(body_parts[0], bytes):
                        return b''.join(body_parts)
                    elif body_parts:
                        # Join as string and encode
                        return ''.join(str(part) for part in body_parts).encode('utf-8')
                    else:
                        return b''
                except Exception as e:
                    logger.warning(f"Failed to consume body iterator: {e}")

        # Method 2: Try to get from raw AMQP message
        if hasattr(original_msg, 'raw_amqp_message') and original_msg.raw_amqp_message:
            raw_body = original_msg.raw_amqp_message.body
            if isinstance(raw_body, (str, bytes)):
                return raw_body
            elif hasattr(raw_body, '__iter__'):
                try:
                    body_parts = list(raw_body)
                    if body_parts and isinstance(body_parts[0], bytes):
                        return b''.join(body_parts)
                    elif body_parts:
                        return ''.join(str(part) for part in body_parts).encode('utf-8')
                except Exception as e:
                    logger.warning(f"Failed to extract from raw AMQP message: {e}")

        # Method 3: Try str() conversion as last resort
        if hasattr(original_msg, 'body'):
            try:
                return str(original_msg.body).encode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to convert body to string: {e}")

        # If all else fails, return empty bytes
        logger.warning("Could not extract message body, returning empty bytes")
        return b''

    except Exception as e:
        logger.error(f"Error extracting message body: {e}")
        return b''


def create_duplicate_message(original_msg) -> ServiceBusMessage:
    """
    Create a duplicate ServiceBusMessage from an original message,
    preserving all properties and metadata.
    """
    try:
        # Extract the message body properly
        message_body = extract_message_body(original_msg)

        # Create new message with extracted body
        new_msg = ServiceBusMessage(
            body=message_body,
            content_type=original_msg.content_type,
            subject=original_msg.subject,
            correlation_id=original_msg.correlation_id,
            # Don't copy message_id to avoid duplicates - let Service Bus generate new ones
            # message_id=original_msg.message_id,
            reply_to=original_msg.reply_to,
            reply_to_session_id=original_msg.reply_to_session_id,
            session_id=original_msg.session_id,
            time_to_live=original_msg.time_to_live
        )

        # Copy application properties
        if original_msg.application_properties:
            for key, value in original_msg.application_properties.items():
                new_msg.application_properties[key] = value

        # Add metadata about the original message
        new_msg.application_properties['OriginalMessageId'] = original_msg.message_id or 'unknown'
        new_msg.application_properties['DuplicatedAt'] = datetime.datetime.utcnow().isoformat()
        new_msg.application_properties['OriginalSequenceNumber'] = str(original_msg.sequence_number)

        return new_msg

    except Exception as e:
        logger.error(f"Error creating duplicate message: {e}")
        logger.error(f"Original message type: {type(original_msg)}")
        if hasattr(original_msg, 'body'):
            logger.error(f"Original body type: {type(original_msg.body)}")
        if hasattr(original_msg, 'message_id'):
            logger.error(f"Original message ID: {original_msg.message_id}")
        raise


def duplicate_queue_messages(queue_name: str, source_client: ServiceBusClient, 
                           dest_client: ServiceBusClient, is_dlq: bool = False) -> int:
    """
    Duplicate messages from source queue to destination queue.
    
    Args:
        queue_name: Name of the queue
        source_client: Source Service Bus client
        dest_client: Destination Service Bus client
        is_dlq: Whether to process dead letter queue messages
    
    Returns:
        Number of messages duplicated
    """
    queue_type = "Dead Letter Queue" if is_dlq else "Active Queue"
    logger.info(f"🔁 Starting duplication from {queue_type}: {queue_name}")
    
    total_duplicated = 0
    
    try:
        # Create receiver for source queue
        receiver = source_client.get_queue_receiver(
            queue_name=queue_name,
            sub_queue="deadletter" if is_dlq else None,
            receive_mode=ServiceBusReceiveMode.PEEK_LOCK
        )
        
        # Create sender for destination queue
        sender = dest_client.get_queue_sender(queue_name=queue_name)
        
        with receiver, sender:
            sequence_number = 0
            
            while True:
                try:
                    # Peek messages from source
                    messages = receiver.peek_messages(
                        max_message_count=BATCH_SIZE,
                        sequence_number=sequence_number
                    )
                    
                    if not messages:
                        logger.info(f"No more messages found in {queue_type}: {queue_name}")
                        break
                    
                    # Process batch of messages
                    batch_messages = []
                    for msg in messages:
                        try:
                            # Update sequence number for next iteration
                            sequence_number = msg.sequence_number + 1
                            
                            # Create duplicate message
                            duplicate_msg = create_duplicate_message(msg)
                            batch_messages.append(duplicate_msg)
                            
                        except Exception as e:
                            logger.error(f"Error processing message {msg.message_id}: {e}")
                            continue
                    
                    # Send batch to destination
                    if batch_messages:
                        retry_count = 0
                        while retry_count < MAX_RETRIES:
                            try:
                                sender.send_messages(batch_messages)
                                total_duplicated += len(batch_messages)
                                logger.info(f"✅ Duplicated {len(batch_messages)} messages from {queue_type}: {queue_name}")
                                break
                            except Exception as e:
                                retry_count += 1
                                if retry_count >= MAX_RETRIES:
                                    logger.error(f"Failed to send batch after {MAX_RETRIES} retries: {e}")
                                    raise
                                else:
                                    logger.warning(f"Retry {retry_count}/{MAX_RETRIES} for sending batch: {e}")
                                    time.sleep(RETRY_DELAY)
                    
                    # Small delay to avoid overwhelming the service
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Error processing batch in {queue_type} {queue_name}: {e}")
                    break
    
    except Exception as e:
        logger.error(f"Error setting up clients for {queue_type} {queue_name}: {e}")
        raise
    
    logger.info(f"🎯 Completed duplication for {queue_type}: {queue_name}. Total messages: {total_duplicated}")
    return total_duplicated


def verify_queue_exists(client: ServiceBusClient, queue_name: str) -> bool:
    """
    Verify that a queue exists in the Service Bus namespace.
    """
    try:
        # Try to get queue properties to verify existence
        with client.get_queue_receiver(queue_name) as receiver:
            # Just creating the receiver is enough to verify the queue exists
            pass
        return True
    except Exception as e:
        logger.error(f"Queue '{queue_name}' does not exist or is not accessible: {e}")
        return False


def main():
    """
    Main function to orchestrate the queue duplication process.
    """
    logger.info("🚀 Starting Azure Service Bus Queue Duplication")
    logger.info(f"📋 Queues to process: {QUEUES}")
    
    total_messages_duplicated = 0
    successful_queues = 0
    failed_queues = 0
    
    try:
        # Create Service Bus clients
        source_client = ServiceBusClient.from_connection_string(SOURCE_CONNECTION_STR)
        dest_client = ServiceBusClient.from_connection_string(DEST_CONNECTION_STR)
        
        logger.info("✅ Service Bus clients created successfully")
        
        # Process each queue
        for queue_name in QUEUES:
            logger.info(f"\n📦 Processing queue: {queue_name}")
            
            try:
                # Verify source queue exists
                if not verify_queue_exists(source_client, queue_name):
                    logger.error(f"❌ Source queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue
                
                # Verify destination queue exists
                if not verify_queue_exists(dest_client, queue_name):
                    logger.error(f"❌ Destination queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue
                
                # Duplicate active messages
                active_count = duplicate_queue_messages(
                    queue_name, source_client, dest_client, is_dlq=False
                )
                
                # Duplicate dead letter queue messages
                dlq_count = duplicate_queue_messages(
                    queue_name, source_client, dest_client, is_dlq=True
                )
                
                queue_total = active_count + dlq_count
                total_messages_duplicated += queue_total
                successful_queues += 1
                
                logger.info(f"✅ Queue '{queue_name}' completed. Active: {active_count}, DLQ: {dlq_count}, Total: {queue_total}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process queue '{queue_name}': {e}")
                failed_queues += 1
                continue
        
        # Final summary
        logger.info(f"\n🎉 Duplication process completed!")
        logger.info(f"📊 Summary:")
        logger.info(f"   • Total messages duplicated: {total_messages_duplicated}")
        logger.info(f"   • Successful queues: {successful_queues}")
        logger.info(f"   • Failed queues: {failed_queues}")
        logger.info(f"   • Source messages remain untouched")
        
        if failed_queues > 0:
            logger.warning(f"⚠️  {failed_queues} queue(s) failed to process. Check logs for details.")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"💥 Critical error in main process: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

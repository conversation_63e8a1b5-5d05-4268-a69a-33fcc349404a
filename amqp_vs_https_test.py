#!/usr/bin/env python3
"""
AMQP vs HTTPS Transport Test for Azure Service Bus

This script compares AMQP and HTTPS transport methods to help identify
which works better for your network environment and configuration.

Usage:
    python3 amqp_vs_https_test.py
"""

import os
import logging
import time
from datetime import datetime
from azure.servicebus import ServiceBusClient, TransportType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
SOURCE_CONNECTION_STR = os.environ.get("SOURCE_SB_CONNECTION_STRING")
DEST_CONNECTION_STR = os.environ.get("DEST_SB_CONNECTION_STRING")

def test_transport_method(connection_string: str, name: str, transport_type: TransportType) -> dict:
    """Test a specific transport method and return results."""
    transport_name = "AMQP" if transport_type == TransportType.Amqp else "HTTPS"
    logger.info(f"🔗 Testing {name} with {transport_name} transport...")
    
    result = {
        'transport': transport_name,
        'success': False,
        'connection_time': None,
        'error': None,
        'auth_success': False
    }
    
    start_time = time.time()
    
    try:
        # Create client with specific transport
        if transport_type == TransportType.Amqp:
            client = ServiceBusClient.from_connection_string(
                connection_string,
                transport_type=TransportType.Amqp,
                connection_timeout=30,
                idle_timeout=300
            )
        else:
            client = ServiceBusClient.from_connection_string(
                connection_string,
                transport_type=TransportType.AmqpOverWebsocket
            )
        
        connection_time = time.time() - start_time
        result['connection_time'] = connection_time
        
        logger.info(f"   • Client created in {connection_time:.2f}s")
        
        # Test authentication
        try:
            with client.get_queue_receiver("__test_nonexistent_queue__") as receiver:
                pass
        except Exception as e:
            error_msg = str(e).lower()
            
            if "not found" in error_msg or "does not exist" in error_msg:
                result['auth_success'] = True
                result['success'] = True
                logger.info(f"   ✅ {transport_name} authentication successful")
            elif "auth" in error_msg or "token" in error_msg:
                result['error'] = f"Authentication failed: {e}"
                logger.error(f"   ❌ {transport_name} authentication failed: {e}")
            elif "timeout" in error_msg or "network" in error_msg:
                result['error'] = f"Network/timeout error: {e}"
                logger.error(f"   ❌ {transport_name} network error: {e}")
            else:
                result['error'] = f"Other error: {e}"
                logger.warning(f"   ⚠️ {transport_name} other error: {e}")
        
        client.close()
        
    except Exception as e:
        connection_time = time.time() - start_time
        result['connection_time'] = connection_time
        result['error'] = str(e)
        logger.error(f"   ❌ {transport_name} client creation failed: {e}")
    
    return result

def compare_transports():
    """Compare AMQP and HTTPS transports for both connections."""
    logger.info("🚀 Starting Transport Method Comparison")
    logger.info(f"⏰ Timestamp: {datetime.now()}")
    logger.info("=" * 60)
    
    if not SOURCE_CONNECTION_STR or not DEST_CONNECTION_STR:
        logger.error("❌ Missing connection strings. Set environment variables:")
        logger.error("   export SOURCE_SB_CONNECTION_STRING='...'")
        logger.error("   export DEST_SB_CONNECTION_STRING='...'")
        return 1
    
    results = []
    
    # Test both connections with both transport methods
    connections = [
        ("Source", SOURCE_CONNECTION_STR),
        ("Destination", DEST_CONNECTION_STR)
    ]
    
    transports = [
        TransportType.Amqp,
        TransportType.AmqpOverWebsocket  # This is HTTPS-based
    ]
    
    for conn_name, conn_str in connections:
        logger.info(f"\n📡 Testing {conn_name} Connection")
        logger.info("-" * 40)
        
        conn_results = []
        
        for transport in transports:
            result = test_transport_method(conn_str, conn_name, transport)
            result['connection'] = conn_name
            conn_results.append(result)
            results.append(result)
        
        # Compare results for this connection
        amqp_result = conn_results[0]
        https_result = conn_results[1]
        
        logger.info(f"\n📊 {conn_name} Comparison:")
        logger.info(f"   AMQP:  {'✅ Success' if amqp_result['success'] else '❌ Failed'} "
                   f"({amqp_result['connection_time']:.2f}s)")
        logger.info(f"   HTTPS: {'✅ Success' if https_result['success'] else '❌ Failed'} "
                   f"({https_result['connection_time']:.2f}s)")
        
        if amqp_result['success'] and https_result['success']:
            faster = "AMQP" if amqp_result['connection_time'] < https_result['connection_time'] else "HTTPS"
            logger.info(f"   🏆 {faster} is faster for {conn_name}")
        elif amqp_result['success']:
            logger.info(f"   🏆 AMQP works, HTTPS doesn't for {conn_name}")
        elif https_result['success']:
            logger.info(f"   🏆 HTTPS works, AMQP doesn't for {conn_name}")
        else:
            logger.info(f"   ❌ Both methods failed for {conn_name}")
    
    # Final recommendations
    logger.info("\n" + "=" * 60)
    logger.info("🎯 RECOMMENDATIONS")
    logger.info("=" * 60)
    
    amqp_successes = sum(1 for r in results if r['transport'] == 'AMQP' and r['success'])
    https_successes = sum(1 for r in results if r['transport'] == 'HTTPS' and r['success'])
    
    if amqp_successes == 2 and https_successes == 2:
        amqp_avg_time = sum(r['connection_time'] for r in results if r['transport'] == 'AMQP') / 2
        https_avg_time = sum(r['connection_time'] for r in results if r['transport'] == 'HTTPS') / 2
        
        if amqp_avg_time < https_avg_time:
            logger.info("✅ RECOMMENDED: Use AMQP (faster and more efficient)")
            logger.info("   • Better performance for high-throughput scenarios")
            logger.info("   • Lower latency")
            logger.info("   • More efficient for batch operations")
        else:
            logger.info("✅ RECOMMENDED: Use HTTPS (more reliable in your network)")
            logger.info("   • Better compatibility with firewalls/proxies")
            logger.info("   • Works through most network restrictions")
    
    elif amqp_successes > https_successes:
        logger.info("✅ RECOMMENDED: Use AMQP")
        logger.info("   • AMQP works better in your environment")
        logger.info("   • HTTPS has connectivity issues")
        
    elif https_successes > amqp_successes:
        logger.info("✅ RECOMMENDED: Use HTTPS (AMQP over WebSocket)")
        logger.info("   • AMQP is blocked by your network/firewall")
        logger.info("   • HTTPS works through proxies and firewalls")
        
    else:
        logger.error("❌ ISSUE: Both transport methods are failing")
        logger.error("   • Check your connection strings")
        logger.error("   • Verify network connectivity")
        logger.error("   • Check Azure Service Bus status")
    
    # Network troubleshooting tips
    logger.info("\n🔧 NETWORK TROUBLESHOOTING:")
    logger.info("   • AMQP uses ports 5671 (secure) / 5672 (non-secure)")
    logger.info("   • HTTPS uses port 443 (standard web traffic)")
    logger.info("   • Corporate firewalls often block AMQP but allow HTTPS")
    logger.info("   • Proxies may interfere with AMQP connections")
    
    # Show detailed errors
    failed_results = [r for r in results if not r['success']]
    if failed_results:
        logger.info("\n❌ DETAILED ERRORS:")
        for result in failed_results:
            logger.info(f"   {result['connection']} - {result['transport']}: {result['error']}")
    
    return 0 if (amqp_successes > 0 or https_successes > 0) else 1

if __name__ == "__main__":
    exit_code = compare_transports()
    exit(exit_code)

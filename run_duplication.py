#!/usr/bin/env python3
"""
Simple runner script for Service Bus queue duplication.
This script provides a command-line interface for the duplication process.
"""

import argparse
import sys
import os
from servicebus_queue_duplicator import main, load_queues_from_file, QUEUES

def setup_environment_from_file(env_file: str):
    """Load environment variables from a file"""
    if not os.path.exists(env_file):
        print(f"❌ Environment file '{env_file}' not found.")
        return False
    
    try:
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
        print(f"✅ Loaded environment variables from '{env_file}'")
        return True
    except Exception as e:
        print(f"❌ Error loading environment file: {e}")
        return False

def main_cli():
    parser = argparse.ArgumentParser(
        description="Duplicate Azure Service Bus queue messages",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_duplication.py
  python run_duplication.py --env-file .env
  python run_duplication.py --queue-file queues.txt
  python run_duplication.py --dry-run
        """
    )
    
    parser.add_argument(
        '--env-file', 
        default='.env',
        help='Environment file with connection strings (default: .env)'
    )
    
    parser.add_argument(
        '--queue-file',
        help='Text file containing queue names (one per line)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be duplicated without actually doing it'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set up logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load environment file if specified
    if args.env_file and os.path.exists(args.env_file):
        if not setup_environment_from_file(args.env_file):
            return 1
    
    # Load queues from file if specified
    if args.queue_file:
        global QUEUES
        QUEUES = load_queues_from_file(args.queue_file)
        print(f"📋 Loaded {len(QUEUES)} queues from '{args.queue_file}'")
    
    # Show configuration
    print(f"🔧 Configuration:")
    print(f"   • Queues to process: {len(QUEUES)}")
    print(f"   • Queue names: {QUEUES}")
    print(f"   • Dry run: {args.dry_run}")
    
    # Check environment variables
    source_conn = os.getenv("SOURCE_SB_CONNECTION_STRING")
    dest_conn = os.getenv("DEST_SB_CONNECTION_STRING")
    
    if not source_conn:
        print("❌ SOURCE_SB_CONNECTION_STRING environment variable is required")
        print("   Set it in your environment or create a .env file")
        return 1
    
    if not dest_conn:
        print("❌ DEST_SB_CONNECTION_STRING environment variable is required")
        print("   Set it in your environment or create a .env file")
        return 1
    
    print("✅ Connection strings configured")
    
    if args.dry_run:
        print("\n🔍 DRY RUN MODE - No messages will be duplicated")
        print("This would duplicate messages from the following queues:")
        for queue in QUEUES:
            print(f"   • {queue}")
        print("\nTo perform actual duplication, run without --dry-run flag")
        return 0
    
    # Confirm before proceeding
    print(f"\n⚠️  This will duplicate messages from {len(QUEUES)} queue(s).")
    print("   Source messages will remain untouched.")
    
    try:
        confirm = input("Continue? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ Operation cancelled by user")
            return 0
    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        return 0
    
    # Run the duplication
    print("\n🚀 Starting duplication process...")
    return main()

if __name__ == "__main__":
    exit_code = main_cli()
    sys.exit(exit_code)

# AI-2-AI INTERACTION RULES - MULTI-PROVIDER SUPPORT
** this is the rules for AI to AI interaction. This document is only allowed to be changed by human. No change can be done by AI Agent **

## Interaction Flow for Augment AI Agent

### MANDATORY: All AI Provider Interactions Must Use Web Browser MCP Tools

**CRITICAL REQUIREMENT**: All interactions with AI providers (ChatGPT, Gemini, Claude) MUST be conducted using the Web browser MCP tools. Manual copying/pasting or other methods are NOT permitted.

### Supported AI Providers:
- **ChatGPT**: https://chat.openai.com
- **Gemini**: https://gemini.google.com  
- **Claude**: https://claude.ai

### Required Browser MCP Tools for AI Provider Interaction:
- `browser_navigate_browsermcp` - Navigate to the selected AI provider URL
- `browser_snapshot_browsermcp` - Capture page state and element references
- `browser_click_browsermcp` - Click on input areas and buttons
- `browser_type_browsermcp` - Type messages into the AI provider's input field
- `browser_wait_browsermcp` - Wait for responses to load

### Step-by-Step Interaction Flow:

1. **Initial Setup**: User gives the AI agent an initial prompt to start the interaction. For example, "I want to build a product that can help people to learn English. Can you help me to build the product?"

2. **Provider Selection**: User specifies which AI provider to use (ChatGPT, Gemini, or Claude), or defaults to Gemini if not specified.

3. **Navigate to AI Provider via Browser MCP**:
   - Use `browser_navigate_browsermcp` to go to the appropriate URL:
     - ChatGPT: https://chat.openai.com
     - Gemini: https://gemini.google.com
     - Claude: https://claude.ai
   - Use `browser_snapshot_browsermcp` to capture the page state
   - Use `browser_click_browsermcp` to click on the input field
   - Use `browser_type_browsermcp` to send the request to the AI provider
   - The AI provider will then act as the product owner, solution architect and project manager

4. **Capture AI Provider Response via Browser MCP**:
   - Use `browser_wait_browsermcp` to wait for the AI provider's response
   - Use `browser_snapshot_browsermcp` to capture the response
   - Extract the AI provider's output and save it to a text file using provider-specific naming:
     - ChatGPT: `chatgpt_input.txt`
     - Gemini: `gemini_input.txt`
     - Claude: `claude_input.txt`
   - Use the [augment_self_messenger.py](augment_self_messenger.py) script: `python3 augment_self_messenger.py -f {provider}_input.txt -y`

5. **Process Task**: Augment AI will continue the task once it receives the input from the messenger script.

6. **Complete Task**: Augment AI Agent will work on the task given by the AI provider and complete it in the IDE.

7. **Send Results Back to AI Provider via Browser MCP**:
   - Use `browser_navigate_browsermcp` to return to the AI provider (if needed)
   - Use `browser_snapshot_browsermcp` to capture current page state
   - Use `browser_click_browsermcp` to click on input field
   - Use `browser_type_browsermcp` to send the completed work to the AI provider
   - Include request for the AI provider to review the output (in the output folder) and provide feedback
   - Attach files if supported by the browser interface

8. **Capture Feedback via Browser MCP**:
   - Use `browser_wait_browsermcp` to wait for the AI provider's feedback
   - Use `browser_snapshot_browsermcp` to capture the feedback response
   - Extract and save feedback to a text file using provider-specific naming:
     - ChatGPT: `temp/chatgpt_feedback.txt`
     - Gemini: `temp/gemini_feedback.txt`
     - Claude: `temp/claude_feedback.txt`

9. **Process Feedback**: Send feedback to self using: `python3 augment_self_messenger.py -f temp/{provider}_feedback.txt -y`

10. **Continue Loop**: Process the feedback and return to step 4 until the feature/request passes the AI provider's review.

11. **Error Handling**: Immediately break the loop if any step encounters browser automation errors or connection issues.

### Provider-Specific Considerations:

#### ChatGPT (chat.openai.com):
- May require login authentication
- Supports file uploads in some plans
- Has conversation history and threading
- May have rate limits based on subscription

#### Gemini (gemini.google.com):
- May require Google account authentication
- Supports multimodal inputs (text, images)
- Has conversation history
- May have usage quotas

#### Claude (claude.ai):
- May require Anthropic account authentication
- Supports file uploads and analysis
- Has conversation history and projects
- May have usage limits based on subscription

### Browser MCP Interaction Requirements:
- **No Manual Intervention**: All AI provider interactions must be fully automated via browser MCP
- **Element Detection**: Always use `browser_snapshot_browsermcp` before interacting with elements
- **Error Recovery**: Implement retry logic for failed browser interactions
- **Response Validation**: Verify that messages were sent and responses received successfully
- **Provider Flexibility**: Handle different UI layouts and authentication flows across providers
- **Session Management**: Maintain active sessions and handle re-authentication if needed

### File Naming Conventions:
- Input files: `{provider}_input.txt` (e.g., `chatgpt_input.txt`, `gemini_input.txt`, `claude_input.txt`)
- Feedback files: `temp/{provider}_feedback.txt` (e.g., `temp/chatgpt_feedback.txt`)
- Output folders: Use consistent naming regardless of provider
- Log files: `{provider}_interaction_log.txt` for debugging purposes

### Provider Selection Logic:
1. If user specifies a provider explicitly, use that provider
2. If no provider specified, default to Gemini
3. If primary provider fails, optionally fall back to alternative providers (with user permission)
4. Log all provider interactions for debugging and audit purposes

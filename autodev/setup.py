#!/usr/bin/env python3
"""
AutoDev Setup Script
Sets up the AutoDev system based on ChatGPT's Master Agent Plan
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing AutoDev dependencies...")
    
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing Python packages"),
        ("playwright install", "Installing Playwright browsers")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def create_project_structure():
    """Create the project directory structure"""
    print("📁 Creating project structure...")
    
    directories = [
        "output",
        "output/sessions",
        "output/products", 
        "output/logs",
        "templates",
        "templates/prompts",
        "config",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   📂 Created {directory}")
    
    print("✅ Project structure created")
    return True

def create_config_files():
    """Create configuration files"""
    print("⚙️ Creating configuration files...")
    
    # Create .env template
    env_template = """# AutoDev Configuration
# Copy this file to .env and fill in your API keys

# AI Provider API Keys (optional - browser automation is primary method)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Browser Settings
HEADLESS_BROWSER=false
BROWSER_TIMEOUT=30000

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=output/logs/autodev.log

# Output Settings
OUTPUT_DIRECTORY=output
SAVE_SESSION_LOGS=true

# Development Settings
DEBUG_MODE=true
"""
    
    with open(".env.example", "w") as f:
        f.write(env_template)
    
    print("✅ Configuration files created")
    return True

def main():
    """Main setup function"""
    print("🎯 AutoDev Setup - AI-Powered Product Development Automation")
    print("=" * 60)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Creating project structure", create_project_structure),
        ("Creating configuration files", create_config_files)
    ]
    
    for description, step_function in steps:
        print(f"\n📋 {description}...")
        if not step_function():
            print(f"❌ Setup failed at: {description}")
            return False
    
    print("\n🎉 AutoDev setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Copy .env.example to .env and add your API keys (optional)")
    print("2. Run: python cli.py demo")
    print("3. Or run: python cli.py build 'your product idea here'")
    print("\n🚀 Ready to build products with AI agents!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

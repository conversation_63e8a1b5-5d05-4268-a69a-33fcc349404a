# AutoDev - AI-Powered Product Development Automation System
# Requirements based on ChatGPT's Master Agent Plan

# Core CLI Framework
click>=8.1.0
rich>=13.7.0

# Browser Automation (Sprint 1)
playwright>=1.40.0
selenium>=4.15.0

# AI Provider APIs
openai>=1.3.0
anthropic>=0.8.0
google-generativeai>=0.3.0

# Async Support
asyncio>=3.4.3
aiofiles>=23.2.0
aiohttp>=3.9.0

# Data Processing & Storage
pandas>=2.1.0
json5>=0.9.0

# Web Framework for Dashboard (Sprint 6)
flask>=3.0.0
flask-socketio>=5.3.0

# Testing Framework (Sprint 5)
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Documentation Generation (Sprint 7)
pdoc>=14.1.0
markdown>=3.5.0

# Logging & Monitoring
loguru>=0.7.0

# Configuration Management
python-dotenv>=1.0.0
pyyaml>=6.0.0

# HTTP Client
httpx>=0.25.0
requests>=2.31.0

# Error Handling & Retry Logic
tenacity>=8.2.0

# Development Tools
black>=23.9.0
flake8>=6.1.0

# Progress Tracking
tqdm>=4.66.0

# Date/Time Handling
python-dateutil>=2.8.0

# JSON Schema Validation
jsonschema>=4.19.0

# Process Management
psutil>=5.9.0

# File System Operations
pathlib2>=2.3.7

# UUID Generation
uuid>=1.30

# Template Engine (for code generation)
jinja2>=3.1.0

# Git Integration (for version control)
gitpython>=3.1.0

# File Watching (for development)
watchdog>=3.0.0

# Memory Profiling (for optimization)
memory-profiler>=0.61.0

# Code Quality
mypy>=1.6.0
pylint>=3.0.0

# Caching
diskcache>=5.6.0

# Security
cryptography>=41.0.0

# Terminal UI Enhancement
textual>=0.44.0

# Browser automation utilities
webdriver-manager>=4.0.0

# Text processing
nltk>=3.8.1
textstat>=0.7.3

# Reporting
tabulate>=0.9.0

# Environment Management
python-decouple>=3.8.0

# Time Zone Support
pytz>=2023.3

# Subprocess Management
subprocess32>=3.5.4

# Signal Handling
signal>=1.0.0

# Network Utilities
urllib3>=2.0.0

# System Information
platform>=1.0.8

# Threading & Multiprocessing
threading>=1.0.0
multiprocessing>=1.0.0

# Temporary Files
tempfile>=1.0.0

# Random Number Generation
random>=1.0.0
secrets>=1.0.0

# Mathematical Operations
math>=1.0.0
statistics>=1.0.0

# Collections
collections>=1.0.0
itertools>=1.0.0

# Functional Programming
functools>=1.0.0
operator>=1.0.0

# Context Management
contextlib>=1.0.0

# Inspection
inspect>=1.0.0

# Type Hints
typing>=3.7.4
typing_extensions>=4.8.0

# Enum Support
enum34>=1.1.10

# Decimal Arithmetic
decimal>=1.70

# Calendar Operations
calendar>=1.0.0

# String Operations
string>=1.0.0

# Text Processing
textwrap>=1.0.0

# Unicode Support
unicodedata>=1.0.0

# Codec Support
codecs>=1.0.0

# Pickle Support
pickle>=1.0.0

# Copy Operations
copy>=1.0.0

# Hash Operations
hashlib>=1.0.0
hmac>=1.0.0

# Compression
zlib>=1.0.0
gzip>=1.0.0

# File Formats
csv>=1.0.0
configparser>=1.0.0

# Internet Protocols
urllib>=1.0.0

# Structured Markup Processing
html>=1.0.0
xml>=1.0.0

# Development Tools
pdb>=1.0.0
traceback>=1.0.0

# Data Persistence
shelve>=1.0.0

# Cryptographic Services
hashlib>=1.0.0
hmac>=1.0.0
secrets>=1.0.0

# Generic Operating System Services
os>=1.0.0
io>=1.0.0
time>=1.0.0
argparse>=1.4.0
logging>=1.0.0
platform>=1.0.8
errno>=1.0.0

# Note: Many modules listed above are built-in Python modules
# They are included for completeness but don't need to be installed via pip

#!/usr/bin/env python3
"""
Simple Auto Messenger
A script that types a message and automatically clicks the send button.
Can read messages from a file or console input.
"""

import time
import sys
import subprocess
import argparse
import os

# Try to import pyautogui for mouse automation (optional)
try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False

class AutoMessenger:
    """Types a message and automatically clicks send"""

    def __init__(self):
        self.platform = sys.platform

    def read_message_from_file(self, file_path: str) -> str:
        """Read message content from a file"""
        try:
            if not os.path.exists(file_path):
                print(f"❌ File not found: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                print(f"❌ File is empty: {file_path}")
                return None

            print(f"📁 Successfully read message from: {file_path}")
            return content

        except Exception as e:
            print(f"❌ Error reading file {file_path}: {e}")
            return None
        
    def create_test_message(self):
        """Create a simple test message"""

        message = """Hello! This is an automated test message.

✅ This message was typed and sent automatically by the script.

The script successfully:
1. Typed this message
2. Clicked the send button automatically

This demonstrates basic automation functionality."""

        return message
    
    def send_message(self, message: str) -> bool:
        """Type message and click send button automatically"""

        print(f"🚀 Preparing to send message...")
        print(f"📨 Message: {message[:50]}...")

        try:
            if self.platform == "darwin":  # macOS
                return self._send_message_macos(message)
            elif self.platform == "win32":  # Windows
                return self._send_message_windows(message)
            elif self.platform.startswith("linux"):  # Linux
                return self._send_message_linux(message)
            else:
                print(f"❌ Platform {self.platform} not supported")
                return False

        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False
    
    def _send_message_macos(self, message: str) -> bool:
        """Type message and click send on macOS"""

        print("🍎 Using macOS automation...")

        # Give user time to focus on the chat input
        print("⏰ You have 3 seconds to click in the chat input box...")
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)

        print("📤 Typing message and clicking send!")

        try:
            # Escape the message for AppleScript
            escaped_message = message.replace('"', '\\"').replace('\n', '\\n').replace('\r', '')

            # Simple AppleScript to type and send
            script = f'''
tell application "System Events"
    keystroke "{escaped_message}"
    delay 0.5
    key code 36
    delay 0.5
    key code 36 using command down
end tell
'''

            # Write and execute script
            script_file = '/tmp/auto_message.scpt'
            with open(script_file, 'w') as f:
                f.write(script)

            result = subprocess.run(['osascript', script_file],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ Message sent!")
                return True
            else:
                print(f"❌ Failed: {result.stderr}")
                return self._send_with_mouse_clicks(message)

        except Exception as e:
            print(f"❌ Error: {e}")
            return self._send_with_mouse_clicks(message)

    def _send_with_mouse_clicks(self, message: str) -> bool:
        """Alternative method using pyautogui"""

        if not PYAUTOGUI_AVAILABLE:
            print("❌ pyautogui not available")
            return False

        try:
            print("🖱️  Using pyautogui...")

            # Type the message
            pyautogui.typewrite(message, interval=0.01)
            time.sleep(0.5)

            # Try different send methods
            pyautogui.hotkey('cmd', 'enter')  # Cmd+Enter
            time.sleep(0.3)
            pyautogui.press('enter')  # Enter
            time.sleep(0.3)
            pyautogui.press('tab')  # Tab then Enter
            pyautogui.press('enter')

            print("✅ Sent with pyautogui!")
            return True

        except Exception as e:
            print(f"❌ pyautogui failed: {e}")
            return False
    
    def _send_message_windows(self, message: str) -> bool:
        """Send message on Windows"""
        return self._send_with_mouse_clicks(message)

    def _send_message_linux(self, message: str) -> bool:
        """Send message on Linux"""
        return self._send_with_mouse_clicks(message)

def main():
    """Main function - send a message from file or console input"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Auto Messenger - Types and sends messages automatically')
    parser.add_argument('-f', '--file', type=str, help='Path to file containing the message to send')
    parser.add_argument('-y', '--yes', action='store_true', help='Skip confirmation prompt')
    args = parser.parse_args()

    messenger = AutoMessenger()

    print("🤖 Simple Auto Messenger")
    print("=" * 30)
    print("This will type a message and click send automatically.")
    print()

    message = None

    # Try to read from file first if specified
    if args.file:
        message = messenger.read_message_from_file(args.file)
        if message is None:
            print("❌ Failed to read from file. Exiting.")
            sys.exit(1)
    else:
        # Get custom message or use default (original behavior)
        custom_message = input("Enter message (or press Enter for default): ").strip()

        if custom_message:
            message = custom_message
        else:
            message = messenger.create_test_message()

    print(f"\n📝 Message to send: {message}")

    # Skip confirmation if -y flag is used
    if args.yes:
        confirm = 'y'
    else:
        confirm = input("\nSend this message? (y/n): ").lower().strip()

    if confirm == 'y':
        success = messenger.send_message(message)

        if success:
            print("\n🎉 SUCCESS! Message should appear above!")
        else:
            print("\n❌ Failed to send automatically.")
            print("💡 Manual copy/paste:")
            print("-" * 30)
            print(message)
            print("-" * 30)
    else:
        print("👋 Cancelled.")

if __name__ == "__main__":
    main()

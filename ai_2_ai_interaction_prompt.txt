AI-2-AI INTERACTION WORKFLOW PROMPT
=====================================

Use this prompt to guide Augment Agent through the complete AI-2-AI interaction workflow for product development automation.

---

PROMPT FOR AUGMENT AGENT:

I want you to demonstrate a complete AI-2-AI interaction workflow for collaborative project execution. Follow these exact steps:

## 🎯 OBJECTIVE:
Create a working system where AI agents collaborate to complete any given project or goal from a single idea, demonstrating true AI-2-AI communication through browser automation.

## 📋 MANDATORY WORKFLOW STEPS:

### STEP 1: INITIAL SETUP
1. Navigate to ChatGPT using browser MCP tools: `browser_navigate_browsermcp` to https://chat.openai.com
2. Send the initial project idea/goal to Cha<PERSON><PERSON><PERSON> acting as Master Agent (Project Manager + Subject Matter Expert + Coordinator)
3. Request comprehensive planning including:
   - Detailed project requirements and objectives
   - Complete approach and methodology
   - Task breakdown with timeline and dependencies
   - Specific implementation tasks

### STEP 2: CAPTURE AND PROCESS RESPONSE
1. Wait for <PERSON><PERSON><PERSON><PERSON>'s complete response using `browser_wait_browsermcp`
2. Take a snapshot to capture the full response using `browser_snapshot_browsermcp`
3. Save ChatGPT's response to a text file (e.g., `chatgpt_response_1.txt`)
4. **MANDATORY**: Use the messenger script to send the response to yourself:
   ```bash
   python3 augment_self_messenger.py -f chatgpt_response_1.txt -y
   ```

### STEP 3: IMPLEMENT BASED ON PLAN
1. Analyze ChatGPT's comprehensive plan
2. Execute the core project components as specified:
   - Create necessary files and structure
   - Build required functionality or content
   - Set up project organization
   - Implement core deliverables
3. Test/validate the implementation to ensure it works
4. Document progress and achievements

### STEP 4: SEND PROGRESS REPORT
1. Create a detailed progress report including:
   - Completed tasks with checkmarks
   - Results and success metrics
   - Current project status
   - Questions for the Master Agent
   - Next steps readiness
2. Navigate back to ChatGPT using browser MCP tools
3. Send the progress report to ChatGPT for review
4. Wait for ChatGPT's feedback and analysis

### STEP 5: CAPTURE FEEDBACK AND CONTINUE CYCLE
1. Capture ChatGPT's feedback response
2. Save the feedback to a new text file (e.g., `chatgpt_response_2.txt`)
3. **MANDATORY**: Use the messenger script again:
   ```bash
   python3 augment_self_messenger.py -f chatgpt_response_2.txt -y
   ```
4. Continue the iterative development cycle based on feedback

### STEP 6: DEMONSTRATE MULTIPLE CYCLES
1. Repeat steps 3-5 for at least 2-3 complete cycles
2. Show continuous improvement and iteration
3. Demonstrate the AI-2-AI collaboration working effectively
4. Build toward a complete, working solution

## 🔧 TECHNICAL REQUIREMENTS:

### Browser MCP Tools Usage:
- `browser_navigate_browsermcp` - Navigate to AI provider URLs
- `browser_snapshot_browsermcp` - Capture page state and responses
- `browser_click_browsermcp` - Click on input areas and buttons
- `browser_type_browsermcp` - Type messages and prompts
- `browser_wait_browsermcp` - Wait for responses to load

### Messenger Script Usage:
- **CRITICAL**: Always use `python3 augment_self_messenger.py -f [filename] -y` after capturing AI responses
- This demonstrates the proper AI-2-AI communication loop
- Save responses to text files before using the messenger script

### Implementation Focus:
- Appropriate tools and technologies for the specific project
- Browser automation for AI-2-AI communication
- Real-time progress tracking and documentation
- Modular approach for easy extension and maintenance
- Complete testing and validation of deliverables

## 🎯 SUCCESS CRITERIA:

### Must Demonstrate:
1. ✅ Multiple AI-2-AI interaction cycles (minimum 3)
2. ✅ Proper use of browser MCP tools throughout
3. ✅ Consistent use of messenger script for feedback loops
4. ✅ Working implementation of planned system
5. ✅ Iterative improvement based on AI feedback
6. ✅ Complete documentation of the process

### Expected Deliverables:
1. Working solution that addresses the project goal
2. Multiple saved AI response files
3. Progress reports and feedback documentation
4. Demonstration of complete AI-2-AI workflow
5. Proof of iterative collaboration process

## 🚨 CRITICAL REQUIREMENTS:

### NEVER Skip These Steps:
1. **Browser MCP Usage**: All AI interactions MUST use browser MCP tools
2. **Messenger Script**: MUST use the script after every AI response capture
3. **Multiple Cycles**: MUST demonstrate at least 2-3 complete interaction cycles
4. **Progress Reporting**: MUST send detailed progress reports back to ChatGPT
5. **Iterative Development**: MUST show continuous improvement based on feedback

### Sample Project Ideas to Use:
- "Create a comprehensive learning curriculum for [specific topic]"
- "Design and implement a workflow optimization system"
- "Build a content creation and management framework"
- "Develop a research and analysis methodology for [domain]"
- "Create a training program with assessment tools"
- "Design a process improvement system for [specific area]"

## 📝 EXAMPLE INTERACTION FLOW:

1. **Initial Request**: Send project idea to ChatGPT → Receive comprehensive plan
2. **Implementation**: Execute core tasks based on plan → Test and validate results
3. **Progress Report**: Send detailed progress update → Receive feedback and next steps
4. **Iteration**: Implement feedback → Send updated progress → Receive further guidance
5. **Completion**: Demonstrate working solution with full AI-2-AI collaboration

## 🎉 FINAL GOAL:

Demonstrate a complete, working AI-2-AI collaboration system where:
- ChatGPT acts as Master Agent providing planning and oversight
- Augment Agent implements the requirements and deliverables
- Multiple AI providers can be coordinated through browser automation
- The entire process is automated and iterative
- Real solutions can be built from simple ideas through AI collaboration

---

REMEMBER: The goal is to show that AI agents can work together autonomously to complete any project or goal, with minimal human intervention, using browser automation as the communication layer.

Start with: "I want to demonstrate AI-2-AI interaction for collaborative project execution. I'll begin by navigating to ChatGPT to establish the Master Agent coordination..."

#!/usr/bin/env python3
"""
Test script to verify message body extraction methods work correctly.
"""

from azure.servicebus import ServiceBusClient, ServiceBusMessage, ServiceBusReceiveMode
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_peek_vs_receive(queue_name: str, connection_string: str):
    """
    Test both peek and receive methods to see which works for body extraction.
    """
    logger.info(f"🧪 Testing message body extraction methods for queue: {queue_name}")
    
    client = ServiceBusClient.from_connection_string(connection_string)
    
    # Test 1: Peek method
    logger.info("\n--- Test 1: PEEK Method ---")
    try:
        with client.get_queue_receiver(queue_name, receive_mode=ServiceBusReceiveMode.PEEK_LOCK) as receiver:
            messages = receiver.peek_messages(max_message_count=1)
            if messages:
                msg = messages[0]
                logger.info(f"Message ID: {msg.message_id}")
                logger.info(f"Body type: {type(msg.body)}")
                logger.info(f"Body is generator: {hasattr(msg.body, '__iter__') and not isinstance(msg.body, (str, bytes))}")
                
                if hasattr(msg.body, '__iter__') and not isinstance(msg.body, (str, bytes)):
                    try:
                        body_parts = list(msg.body)
                        logger.info(f"Body parts count: {len(body_parts)}")
                        if body_parts:
                            logger.info(f"First part type: {type(body_parts[0])}")
                            combined = b''.join(body_parts) if isinstance(body_parts[0], bytes) else ''.join(str(p) for p in body_parts)
                            logger.info(f"✅ PEEK: Successfully extracted body, length: {len(combined)}")
                        else:
                            logger.info("❌ PEEK: Body parts list is empty")
                    except Exception as e:
                        logger.error(f"❌ PEEK: Failed to extract body: {e}")
                else:
                    logger.info(f"✅ PEEK: Body is already proper type: {type(msg.body)}")
            else:
                logger.info("No messages found with PEEK")
    except Exception as e:
        logger.error(f"❌ PEEK method failed: {e}")
    
    # Test 2: Receive method
    logger.info("\n--- Test 2: RECEIVE Method ---")
    try:
        with client.get_queue_receiver(queue_name, receive_mode=ServiceBusReceiveMode.PEEK_LOCK) as receiver:
            messages = receiver.receive_messages(max_message_count=1, max_wait_time=10)
            if messages:
                msg = messages[0]
                logger.info(f"Message ID: {msg.message_id}")
                logger.info(f"Body type: {type(msg.body)}")
                logger.info(f"Body is generator: {hasattr(msg.body, '__iter__') and not isinstance(msg.body, (str, bytes))}")
                
                try:
                    # Try to access body directly
                    body = msg.body
                    if isinstance(body, (str, bytes)):
                        logger.info(f"✅ RECEIVE: Body is proper type, length: {len(body)}")
                    elif hasattr(body, '__iter__'):
                        body_parts = list(body)
                        if body_parts:
                            combined = b''.join(body_parts) if isinstance(body_parts[0], bytes) else ''.join(str(p) for p in body_parts)
                            logger.info(f"✅ RECEIVE: Successfully extracted body from iterator, length: {len(combined)}")
                        else:
                            logger.info("❌ RECEIVE: Body iterator is empty")
                    else:
                        logger.info(f"✅ RECEIVE: Body type: {type(body)}, content: {str(body)[:100]}")
                    
                    # Test creating a new ServiceBusMessage
                    new_msg = ServiceBusMessage(body=body)
                    logger.info("✅ RECEIVE: Successfully created new ServiceBusMessage")
                    
                except Exception as e:
                    logger.error(f"❌ RECEIVE: Failed to extract body: {e}")
                
                # Abandon the message to put it back
                try:
                    receiver.abandon_message(msg)
                    logger.info("✅ Message abandoned successfully")
                except Exception as e:
                    logger.error(f"Failed to abandon message: {e}")
            else:
                logger.info("No messages found with RECEIVE")
    except Exception as e:
        logger.error(f"❌ RECEIVE method failed: {e}")

def main():
    connection_string = os.getenv("SOURCE_SB_CONNECTION_STRING")
    if not connection_string:
        logger.error("SOURCE_SB_CONNECTION_STRING environment variable is required")
        return 1
    
    queue_name = input("Enter queue name to test: ").strip()
    if not queue_name:
        logger.error("Queue name is required")
        return 1
    
    try:
        test_peek_vs_receive(queue_name, connection_string)
        logger.info("🎉 Test completed")
        return 0
    except Exception as e:
        logger.error(f"💥 Test failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

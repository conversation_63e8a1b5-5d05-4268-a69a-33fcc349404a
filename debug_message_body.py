#!/usr/bin/env python3
"""
Debug script to inspect Service Bus message body types and content.
This helps understand how to properly extract message bodies.
"""

from azure.servicebus import ServiceBusClient, ServiceBusReceiveMode
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_message_body(queue_name: str, connection_string: str, max_messages: int = 5):
    """
    Debug function to inspect message body types and content.
    """
    logger.info(f"🔍 Debugging message bodies in queue: {queue_name}")
    
    try:
        client = ServiceBusClient.from_connection_string(connection_string)
        
        with client.get_queue_receiver(queue_name, receive_mode=ServiceBusReceiveMode.PEEK_LOCK) as receiver:
            messages = receiver.peek_messages(max_message_count=max_messages)
            
            if not messages:
                logger.info("No messages found in queue")
                return
            
            for i, msg in enumerate(messages, 1):
                logger.info(f"\n--- Message {i} ---")
                logger.info(f"Message ID: {msg.message_id}")
                logger.info(f"Sequence Number: {msg.sequence_number}")
                logger.info(f"Content Type: {msg.content_type}")
                logger.info(f"Subject: {msg.subject}")
                
                # Debug body
                logger.info(f"Body type: {type(msg.body)}")
                logger.info(f"Body is None: {msg.body is None}")
                
                if hasattr(msg.body, '__iter__') and not isinstance(msg.body, (str, bytes)):
                    logger.info("Body is iterable (likely generator)")
                    try:
                        # Try to peek at the first item without consuming
                        body_iter = iter(msg.body)
                        first_item = next(body_iter, None)
                        if first_item is not None:
                            logger.info(f"First body item type: {type(first_item)}")
                            logger.info(f"First body item (first 100 chars): {str(first_item)[:100]}")
                        else:
                            logger.info("Body iterator is empty")
                    except Exception as e:
                        logger.error(f"Error inspecting body iterator: {e}")
                else:
                    logger.info(f"Body content (first 100 chars): {str(msg.body)[:100]}")
                
                # Debug raw AMQP message
                if hasattr(msg, 'raw_amqp_message') and msg.raw_amqp_message:
                    logger.info(f"Raw AMQP message body type: {type(msg.raw_amqp_message.body)}")
                    try:
                        raw_body = msg.raw_amqp_message.body
                        if hasattr(raw_body, '__iter__') and not isinstance(raw_body, (str, bytes)):
                            logger.info("Raw AMQP body is iterable")
                            raw_iter = iter(raw_body)
                            first_raw = next(raw_iter, None)
                            if first_raw is not None:
                                logger.info(f"First raw item type: {type(first_raw)}")
                                logger.info(f"First raw item (first 100 chars): {str(first_raw)[:100]}")
                        else:
                            logger.info(f"Raw AMQP body (first 100 chars): {str(raw_body)[:100]}")
                    except Exception as e:
                        logger.error(f"Error inspecting raw AMQP body: {e}")
                
                # Debug application properties
                if msg.application_properties:
                    logger.info(f"Application properties: {dict(msg.application_properties)}")
                else:
                    logger.info("No application properties")
                
                # Try different extraction methods
                logger.info("\n--- Extraction Methods ---")
                
                # Method 1: Direct body access
                try:
                    if isinstance(msg.body, (str, bytes)):
                        logger.info("✅ Method 1 (direct): Body is already string/bytes")
                    elif hasattr(msg.body, '__iter__'):
                        body_parts = list(msg.body)
                        if body_parts:
                            combined = b''.join(body_parts) if isinstance(body_parts[0], bytes) else ''.join(str(p) for p in body_parts)
                            logger.info(f"✅ Method 1 (iterator): Combined {len(body_parts)} parts, type: {type(combined)}")
                        else:
                            logger.info("⚠️ Method 1: Body iterator is empty")
                except Exception as e:
                    logger.error(f"❌ Method 1 failed: {e}")
                
                # Method 2: Raw AMQP
                try:
                    if hasattr(msg, 'raw_amqp_message') and msg.raw_amqp_message:
                        raw_body = msg.raw_amqp_message.body
                        if isinstance(raw_body, (str, bytes)):
                            logger.info("✅ Method 2 (raw AMQP): Body is string/bytes")
                        elif hasattr(raw_body, '__iter__'):
                            raw_parts = list(raw_body)
                            if raw_parts:
                                combined = b''.join(raw_parts) if isinstance(raw_parts[0], bytes) else ''.join(str(p) for p in raw_parts)
                                logger.info(f"✅ Method 2 (raw AMQP iterator): Combined {len(raw_parts)} parts")
                            else:
                                logger.info("⚠️ Method 2: Raw AMQP iterator is empty")
                except Exception as e:
                    logger.error(f"❌ Method 2 failed: {e}")
                
    except Exception as e:
        logger.error(f"Error debugging messages: {e}")
        raise

def main():
    """Main function for debugging"""
    # Get configuration
    connection_string = os.getenv("SOURCE_SB_CONNECTION_STRING")
    if not connection_string:
        logger.error("SOURCE_SB_CONNECTION_STRING environment variable is required")
        return 1
    
    queue_name = input("Enter queue name to debug: ").strip()
    if not queue_name:
        logger.error("Queue name is required")
        return 1
    
    max_messages = input("Enter max messages to inspect (default 5): ").strip()
    max_messages = int(max_messages) if max_messages.isdigit() else 5
    
    try:
        debug_message_body(queue_name, connection_string, max_messages)
        logger.info("🎉 Debug completed successfully")
        return 0
    except Exception as e:
        logger.error(f"💥 Debug failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

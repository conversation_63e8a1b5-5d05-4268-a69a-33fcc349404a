# AI-2-AI INTERACTION RULES - DO NOT CHANGE
** this is the rules for AI to AI interaction. This document is only allowed to be changed by human. No change can be done by AI Agent **

## Interaction Flow for Augment AI Agent

### MANDATORY: All Gemini Interactions Must Use Web Browser MCP Tools

**CRITICAL REQUIREMENT**: All interactions with Gemini AI MUST be conducted using the Web browser MCP tools. Manual copying/pasting or other methods are NOT permitted.

### Required Browser MCP Tools for Gemini Interaction:
- `browser_navigate_browsermcp` - Navigate to https://gemini.google.com
- `browser_snapshot_browsermcp` - Capture page state and element references
- `browser_click_browsermcp` - Click on input areas and buttons
- `browser_type_browsermcp` - Type messages into <PERSON>'s input field
- `browser_wait_browsermcp` - Wait for responses to load

### Step-by-Step Interaction Flow:

1. **Initial Setup**: User gives the AI agent an initial prompt to start the interaction. For example, "I want to build a product that can help people to learn English. Can you help me to build the product?"

2. **Navigate to Gemini via Browser MCP**:
   - Use `browser_navigate_browsermcp` to go to https://gemini.google.com
   - Use `browser_snapshot_browsermcp` to capture the page state
   - Use `browser_click_browsermcp` to click on the input field
   - Use `browser_type_browsermcp` to send the request to Gemini AI
   - The Gemini AI will then act as the product owner, solution architect and project manager

3. **Capture Gemini Response via Browser MCP**:
   - Use `browser_wait_browsermcp` to wait for Gemini's response
   - Use `browser_snapshot_browsermcp` to capture the response
   - Extract Gemini's output and save it to a text file (e.g., gemini_input.txt)
   - Use the [augment_self_messenger.py](augment_self_messenger.py) script: `python3 augment_self_messenger.py -f gemini_input.txt -y`

4. **Process Task**: Augment AI will continue the task once it receives the input from the messenger script.

5. **Complete Task**: Augment AI Agent will work on the task given by Gemini AI and complete it in the IDE.

6. **Send Results Back to Gemini via Browser MCP**:
   - Use `browser_navigate_browsermcp` to return to Gemini (if needed)
   - Use `browser_snapshot_browsermcp` to capture current page state
   - Use `browser_click_browsermcp` to click on input field
   - Use `browser_type_browsermcp` to send the completed work to Gemini
   - Include request for Gemini to review the output and provide feedback
   - Attach files if supported by the browser interface

7. **Capture Feedback via Browser MCP**:
   - Use `browser_wait_browsermcp` to wait for Gemini's feedback
   - Use `browser_snapshot_browsermcp` to capture the feedback response
   - Extract and save feedback to a text file (e.g., gemini_feedback.txt)

8. **Process Feedback**: Send feedback to self using: `python3 augment_self_messenger.py -f gemini_feedback.txt -y`

9. **Continue Loop**: Process the feedback and return to step 3 until the feature/request passes Gemini's review.

10. **Error Handling**: Immediately break the loop if any step encounters browser automation errors or connection issues.

### Browser MCP Interaction Requirements:
- **No Manual Intervention**: All Gemini interactions must be fully automated via browser MCP
- **Element Detection**: Always use `browser_snapshot_browsermcp` before interacting with elements
- **Error Recovery**: Implement retry logic for failed browser interactions
- **Response Validation**: Verify that messages were sent and responses received successfully
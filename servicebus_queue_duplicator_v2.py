#!/usr/bin/env python3
"""
Azure Service Bus Queue Message Duplicator - Version 2

This version uses receive + abandon to properly access message bodies,
which resolves the generator issue with peek operations.

Requirements:
- azure-servicebus>=7.11.0
- Python 3.7+
"""

from azure.servicebus import ServiceBusClient, ServiceBusMessage, ServiceBusReceiveMode
import os
import logging
import time
import datetime
from typing import List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
SOURCE_CONNECTION_STR = os.getenv("SOURCE_SB_CONNECTION_STRING")
DEST_CONNECTION_STR = os.getenv("DEST_SB_CONNECTION_STRING")

# Validate environment variables
if not SOURCE_CONNECTION_STR:
    raise ValueError("SOURCE_SB_CONNECTION_STRING environment variable is required")
if not DEST_CONNECTION_STR:
    raise ValueError("DEST_SB_CONNECTION_STRING environment variable is required")

# List of queues to migrate
QUEUES = [
    "error-subscription-audit-aks-dev",
    # Add more queues as needed
]

# Configuration options
BATCH_SIZE = 10  # Number of messages to process in each batch
MAX_RETRIES = 3  # Maximum number of retries for failed operations
RETRY_DELAY = 1  # Delay between retries in seconds


def extract_message_body_from_peek(original_msg):
    """
    Extract message body from a peeked message, handling the generator issue.
    This function safely extracts the body without consuming/removing the message.
    """
    try:
        if not hasattr(original_msg, 'body') or original_msg.body is None:
            return b''

        body = original_msg.body

        # If body is already string or bytes, return as-is
        if isinstance(body, (str, bytes)):
            return body

        # If body is a generator/iterator (common with peek), consume it safely
        if hasattr(body, '__iter__'):
            try:
                body_parts = list(body)
                if not body_parts:
                    return b''

                # Join bytes parts
                if isinstance(body_parts[0], bytes):
                    return b''.join(body_parts)
                # Join string parts and encode
                else:
                    return ''.join(str(part) for part in body_parts).encode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to consume body iterator: {e}")

        # Try to get from raw AMQP message as fallback
        if hasattr(original_msg, 'raw_amqp_message') and original_msg.raw_amqp_message:
            try:
                raw_body = original_msg.raw_amqp_message.body
                if isinstance(raw_body, (str, bytes)):
                    return raw_body
                elif hasattr(raw_body, '__iter__'):
                    raw_parts = list(raw_body)
                    if raw_parts:
                        if isinstance(raw_parts[0], bytes):
                            return b''.join(raw_parts)
                        else:
                            return ''.join(str(part) for part in raw_parts).encode('utf-8')
            except Exception as e:
                logger.warning(f"Failed to extract from raw AMQP message: {e}")

        # Last resort: convert to string
        try:
            return str(body).encode('utf-8')
        except Exception as e:
            logger.warning(f"Failed to convert body to string: {e}")
            return b''

    except Exception as e:
        logger.error(f"Error extracting message body: {e}")
        return b''


def create_duplicate_message(original_msg) -> ServiceBusMessage:
    """
    Create a duplicate ServiceBusMessage from an original message,
    preserving all properties and metadata.
    Uses safe body extraction for peeked messages.
    """
    try:
        # Extract the message body safely from peeked message
        message_body = extract_message_body_from_peek(original_msg)

        # Create new message with extracted body
        new_msg = ServiceBusMessage(
            body=message_body,
            content_type=original_msg.content_type,
            subject=original_msg.subject,
            correlation_id=original_msg.correlation_id,
            # Don't copy message_id to avoid duplicates - let Service Bus generate new ones
            reply_to=original_msg.reply_to,
            reply_to_session_id=original_msg.reply_to_session_id,
            session_id=original_msg.session_id,
            time_to_live=original_msg.time_to_live
        )

        # Copy application properties
        if original_msg.application_properties:
            for key, value in original_msg.application_properties.items():
                new_msg.application_properties[key] = value

        # Add metadata about the original message
        new_msg.application_properties['OriginalMessageId'] = original_msg.message_id or 'unknown'
        new_msg.application_properties['DuplicatedAt'] = datetime.datetime.utcnow().isoformat()
        new_msg.application_properties['OriginalSequenceNumber'] = str(original_msg.sequence_number)
        new_msg.application_properties['SourceQueue'] = original_msg.delivery_count if hasattr(original_msg, 'delivery_count') else 'unknown'

        return new_msg

    except Exception as e:
        logger.error(f"Error creating duplicate message: {e}")
        logger.error(f"Original message type: {type(original_msg)}")
        logger.error(f"Original body type: {type(original_msg.body) if hasattr(original_msg, 'body') else 'No body'}")
        raise


def duplicate_queue_messages_peek_only(queue_name: str, source_client: ServiceBusClient,
                                     dest_client: ServiceBusClient, is_dlq: bool = False) -> int:
    """
    Duplicate messages using ONLY peek operations - completely non-destructive.
    This ensures source messages are never touched, received, or modified in any way.

    Args:
        queue_name: Name of the queue
        source_client: Source Service Bus client
        dest_client: Destination Service Bus client
        is_dlq: Whether to process dead letter queue messages

    Returns:
        Number of messages duplicated
    """
    queue_type = "Dead Letter Queue" if is_dlq else "Active Queue"
    logger.info(f"� Starting PEEK-ONLY duplication from {queue_type}: {queue_name}")
    logger.info(f"⚠️  Using PEEK mode - source messages will remain completely untouched")

    total_duplicated = 0

    try:
        # Create receiver for source queue - PEEK mode only
        receiver = source_client.get_queue_receiver(
            queue_name=queue_name,
            sub_queue="deadletter" if is_dlq else None,
            receive_mode=ServiceBusReceiveMode.PEEK_LOCK  # This doesn't actually lock when peeking
        )

        # Create sender for destination queue
        sender = dest_client.get_queue_sender(queue_name=queue_name)

        with receiver, sender:
            sequence_number = 0  # Start from the beginning
            consecutive_empty_batches = 0
            max_empty_batches = 3  # Stop after 3 consecutive empty batches

            while consecutive_empty_batches < max_empty_batches:
                try:
                    # PEEK messages - this is completely non-destructive
                    messages = receiver.peek_messages(
                        max_message_count=BATCH_SIZE,
                        sequence_number=sequence_number
                    )

                    if not messages:
                        consecutive_empty_batches += 1
                        logger.info(f"No messages found starting from sequence {sequence_number} (attempt {consecutive_empty_batches}/{max_empty_batches})")
                        if consecutive_empty_batches >= max_empty_batches:
                            logger.info(f"No more messages found in {queue_type}: {queue_name}")
                            break
                        # Try advancing sequence number in case there are gaps
                        sequence_number += BATCH_SIZE
                        continue

                    consecutive_empty_batches = 0  # Reset counter

                    # Process batch of messages
                    batch_messages = []
                    max_sequence = sequence_number

                    for msg in messages:
                        try:
                            # Update sequence number for next iteration
                            max_sequence = max(max_sequence, msg.sequence_number + 1)

                            # Create duplicate message using safe extraction
                            duplicate_msg = create_duplicate_message(msg)
                            batch_messages.append(duplicate_msg)

                            logger.debug(f"Processed message {msg.message_id} (seq: {msg.sequence_number})")

                        except Exception as e:
                            logger.error(f"Error processing message {getattr(msg, 'message_id', 'unknown')}: {e}")
                            # Continue with other messages
                            continue

                    # Update sequence number for next batch
                    sequence_number = max_sequence

                    # Send batch to destination
                    if batch_messages:
                        retry_count = 0
                        while retry_count < MAX_RETRIES:
                            try:
                                sender.send_messages(batch_messages)
                                total_duplicated += len(batch_messages)
                                logger.info(f"✅ Duplicated {len(batch_messages)} messages from {queue_type}: {queue_name} (seq: {sequence_number-len(batch_messages)}-{sequence_number-1})")
                                break
                            except Exception as e:
                                retry_count += 1
                                if retry_count >= MAX_RETRIES:
                                    logger.error(f"Failed to send batch after {MAX_RETRIES} retries: {e}")
                                    # Continue to next batch even if this one fails
                                    break
                                else:
                                    logger.warning(f"Retry {retry_count}/{MAX_RETRIES} for sending batch: {e}")
                                    time.sleep(RETRY_DELAY)

                    # Small delay to avoid overwhelming the service
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"Error processing batch in {queue_type} {queue_name}: {e}")
                    # Try to continue with next sequence number
                    sequence_number += BATCH_SIZE
                    consecutive_empty_batches += 1
                    continue

    except Exception as e:
        logger.error(f"Error setting up clients for {queue_type} {queue_name}: {e}")
        raise

    logger.info(f"🎯 Completed PEEK-ONLY duplication for {queue_type}: {queue_name}. Total messages: {total_duplicated}")
    logger.info(f"✅ Source queue remains completely untouched - no messages were received or modified")
    return total_duplicated


def verify_queue_exists(client: ServiceBusClient, queue_name: str) -> bool:
    """
    Verify that a queue exists in the Service Bus namespace.
    """
    try:
        # Try to get queue properties to verify existence
        with client.get_queue_receiver(queue_name, max_wait_time=5) as receiver:
            # Just creating the receiver is enough to verify the queue exists
            pass
        return True
    except Exception as e:
        logger.error(f"Queue '{queue_name}' does not exist or is not accessible: {e}")
        return False


def main():
    """
    Main function to orchestrate the queue duplication process.
    """
    logger.info("🚀 Starting Azure Service Bus Queue Duplication (Version 2)")
    logger.info(f"📋 Queues to process: {QUEUES}")
    
    total_messages_duplicated = 0
    successful_queues = 0
    failed_queues = 0
    
    try:
        # Create Service Bus clients
        source_client = ServiceBusClient.from_connection_string(SOURCE_CONNECTION_STR)
        dest_client = ServiceBusClient.from_connection_string(DEST_CONNECTION_STR)
        
        logger.info("✅ Service Bus clients created successfully")
        
        # Process each queue
        for queue_name in QUEUES:
            logger.info(f"\n📦 Processing queue: {queue_name}")
            
            try:
                # Verify source queue exists
                if not verify_queue_exists(source_client, queue_name):
                    logger.error(f"❌ Source queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue
                
                # Verify destination queue exists
                if not verify_queue_exists(dest_client, queue_name):
                    logger.error(f"❌ Destination queue '{queue_name}' does not exist. Skipping.")
                    failed_queues += 1
                    continue
                
                # Duplicate active messages
                active_count = duplicate_queue_messages_v2(
                    queue_name, source_client, dest_client, is_dlq=False
                )
                
                # Duplicate dead letter queue messages
                dlq_count = duplicate_queue_messages_v2(
                    queue_name, source_client, dest_client, is_dlq=True
                )
                
                queue_total = active_count + dlq_count
                total_messages_duplicated += queue_total
                successful_queues += 1
                
                logger.info(f"✅ Queue '{queue_name}' completed. Active: {active_count}, DLQ: {dlq_count}, Total: {queue_total}")
                
            except Exception as e:
                logger.error(f"❌ Failed to process queue '{queue_name}': {e}")
                failed_queues += 1
                continue
        
        # Final summary
        logger.info(f"\n🎉 Duplication process completed!")
        logger.info(f"📊 Summary:")
        logger.info(f"   • Total messages duplicated: {total_messages_duplicated}")
        logger.info(f"   • Successful queues: {successful_queues}")
        logger.info(f"   • Failed queues: {failed_queues}")
        logger.info(f"   • Source messages remain untouched (abandoned back to queue)")
        
        if failed_queues > 0:
            logger.warning(f"⚠️  {failed_queues} queue(s) failed to process. Check logs for details.")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"💥 Critical error in main process: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

# Dependencies for AutoDev - AI-Powered Product Development Automation System
# Based on ChatGPT's technical architecture recommendations

# === CORE AUTODEV DEPENDENCIES ===
# Browser Automation (Primary)
playwright>=1.40.0
selenium>=4.15.0

# AI Provider APIs
openai>=1.3.0
anthropic>=0.8.0
google-generativeai>=0.3.0

# Web Framework for Dashboard
flask>=3.0.0
flask-socketio>=5.3.0

# Async Support
aiofiles>=23.2.0
aiohttp>=3.9.0

# Data Processing
pandas>=2.1.0
json5>=0.9.0

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Logging & Monitoring
loguru>=0.7.0

# CLI & Terminal UI
click>=8.1.0
rich>=13.7.0
tqdm>=4.66.0

# Configuration Management
python-dotenv>=1.0.0
pyyaml>=6.0.0

# HTTP Client
httpx>=0.25.0
requests>=2.31.0

# Error Handling & Retry Logic
tenacity>=8.2.0

# Documentation Generation
pdoc>=14.1.0
markdown>=3.5.0

# Development Tools
black>=23.9.0
flake8>=6.1.0

# === LEGACY BRIDGE SYSTEM DEPENDENCIES ===
# Core automation dependencies (existing)
pyautogui>=0.9.54
webdriver-manager>=4.0.0

# Text processing (existing)
nltk>=3.8.1
textstat>=0.7.3

# Reporting (existing)
tabulate>=0.9.0

# Note: Standard library modules used (included with Python):
# json, re, datetime, subprocess, time, sys, typing, asyncio, logging

# Azure Service Bus Queue Message Duplicator

This tool duplicates messages from source Azure Service Bus queues to destination Service Bus queues, preserving all message properties and metadata. It handles both active messages and dead letter queue messages.

## Features

- ✅ **Non-destructive**: Source messages remain untouched
- ✅ **Complete duplication**: Preserves all message properties and metadata
- ✅ **Dead Letter Queue support**: Duplicates both active and DLQ messages
- ✅ **Batch processing**: Efficient handling of large message volumes
- ✅ **Error handling**: Robust retry logic and error recovery
- ✅ **Logging**: Comprehensive logging for monitoring and debugging
- ✅ **Validation**: Verifies queue existence before processing
- ✅ **Flexible configuration**: Support for environment files and queue lists

## Prerequisites

- Python 3.7 or higher
- Azure Service Bus namespaces (source and destination)
- Appropriate permissions on both Service Bus namespaces

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r servicebus_requirements.txt
   ```

2. **Set up configuration:**
   ```bash
   cp config.env.example .env
   # Edit .env with your actual connection strings
   ```

## Configuration

### Environment Variables

Create a `.env` file with your Service Bus connection strings:

```bash
# Source Service Bus connection string
SOURCE_SB_CONNECTION_STRING=Endpoint=sb://your-source-servicebus.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-source-key

# Destination Service Bus connection string  
DEST_SB_CONNECTION_STRING=Endpoint=sb://your-dest-servicebus.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-dest-key
```

### Queue Configuration

**Option 1: Edit the script directly**
```python
QUEUES = [
    "error-subscription-audit-aks-dev",
    "queue-cad-training",
    "subscription-audit-aks-training"
]
```

**Option 2: Use a queue file**
Create a `queues.txt` file:
```
error-subscription-audit-aks-dev
queue-cad-training
subscription-audit-aks-training
# Comments are supported
```

## Usage

### Basic Usage

```bash
# Run with default configuration
python run_duplication.py

# Run with custom environment file
python run_duplication.py --env-file production.env

# Run with custom queue file
python run_duplication.py --queue-file my-queues.txt

# Dry run to see what would be duplicated
python run_duplication.py --dry-run

# Verbose logging
python run_duplication.py --verbose
```

### Direct Script Usage

```bash
# Set environment variables and run directly
export SOURCE_SB_CONNECTION_STRING="your-source-connection-string"
export DEST_SB_CONNECTION_STRING="your-dest-connection-string"
python servicebus_queue_duplicator.py
```

## How It Works

1. **Connection**: Establishes connections to both source and destination Service Bus namespaces
2. **Validation**: Verifies that all specified queues exist in both namespaces
3. **Message Retrieval**: Uses PEEK_LOCK mode to read messages without removing them from source
4. **Message Duplication**: Creates new messages with identical properties and content
5. **Batch Processing**: Sends messages in configurable batches for efficiency
6. **Dead Letter Processing**: Separately handles dead letter queue messages
7. **Logging**: Provides detailed progress and error information

## Message Properties Preserved

The tool preserves all message properties including:

- Message body and content type
- Subject and correlation ID
- Message ID and reply-to information
- Session ID and time-to-live
- All custom application properties
- Dead letter queue metadata

## Error Handling

- **Retry Logic**: Automatic retries for transient failures
- **Queue Validation**: Verifies queue existence before processing
- **Batch Recovery**: Continues processing even if individual batches fail
- **Comprehensive Logging**: Detailed error messages and stack traces

## Configuration Options

You can modify these constants in the script:

```python
BATCH_SIZE = 10        # Messages per batch
MAX_RETRIES = 3        # Retry attempts for failed operations
RETRY_DELAY = 1        # Seconds between retries
```

## Monitoring and Logging

The tool provides comprehensive logging:

- **INFO**: Progress updates and successful operations
- **WARNING**: Non-critical issues and retries
- **ERROR**: Failed operations and errors
- **DEBUG**: Detailed operation information (use --verbose)

Example log output:
```
2025-01-14 13:45:00 - INFO - 🚀 Starting Azure Service Bus Queue Duplication
2025-01-14 13:45:01 - INFO - ✅ Service Bus clients created successfully
2025-01-14 13:45:02 - INFO - 🔁 Starting duplication from Active Queue: error-subscription-audit-aks-dev
2025-01-14 13:45:03 - INFO - ✅ Duplicated 10 messages from Active Queue: error-subscription-audit-aks-dev
2025-01-14 13:45:04 - INFO - 🎯 Completed duplication for Active Queue: error-subscription-audit-aks-dev. Total messages: 25
```

## Troubleshooting

### Common Issues

1. **Connection String Issues**
   ```
   Error: Queue 'queue-name' does not exist or is not accessible
   ```
   - Verify connection strings are correct
   - Check that the queue exists in both namespaces
   - Ensure proper permissions (Manage, Send, Listen)

2. **Permission Issues**
   ```
   Error: Unauthorized access
   ```
   - Verify the connection string has appropriate permissions
   - Check that the shared access key is valid

3. **Network Issues**
   ```
   Error: Connection timeout
   ```
   - Check network connectivity to Azure
   - Verify firewall settings
   - Consider increasing retry settings

### Debug Mode

Run with verbose logging to get detailed information:
```bash
python run_duplication.py --verbose
```

## Safety Considerations

- **Non-destructive**: Source messages are never modified or deleted
- **Idempotent**: Can be run multiple times safely (may create duplicates)
- **Validation**: Verifies queue existence before processing
- **Dry Run**: Test configuration without making changes

## Performance Considerations

- **Batch Size**: Larger batches are more efficient but use more memory
- **Concurrent Processing**: The tool processes queues sequentially for safety
- **Rate Limiting**: Small delays prevent overwhelming the service
- **Memory Usage**: Processes messages in batches to manage memory

## Example Output

```
🚀 Starting Azure Service Bus Queue Duplication
📋 Queues to process: ['error-subscription-audit-aks-dev']
✅ Service Bus clients created successfully

📦 Processing queue: error-subscription-audit-aks-dev
🔁 Starting duplication from Active Queue: error-subscription-audit-aks-dev
✅ Duplicated 10 messages from Active Queue: error-subscription-audit-aks-dev
🔁 Starting duplication from Dead Letter Queue: error-subscription-audit-aks-dev
✅ Duplicated 5 messages from Dead Letter Queue: error-subscription-audit-aks-dev
✅ Queue 'error-subscription-audit-aks-dev' completed. Active: 25, DLQ: 5, Total: 30

🎉 Duplication process completed!
📊 Summary:
   • Total messages duplicated: 30
   • Successful queues: 1
   • Failed queues: 0
   • Source messages remain untouched
```

## Support

For issues or questions:
1. Check the logs for detailed error information
2. Verify your configuration and permissions
3. Test with a single queue first
4. Use dry-run mode to validate setup

#!/usr/bin/env python3
"""
Azure Service Bus Connection Diagnostic Tool

This script helps diagnose connection and authentication issues with Azure Service Bus.
It performs comprehensive tests to identify the root cause of connection failures.

Usage:
    python3 servicebus_connection_diagnostic.py
"""

import os
import logging
import re
from datetime import datetime
from azure.servicebus import ServiceBusClient
from typing import Dict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration - Use environment variables
SOURCE_CONNECTION_STR = os.environ.get("SOURCE_SB_CONNECTION_STRING")
DEST_CONNECTION_STR = os.environ.get("DEST_SB_CONNECTION_STRING")

def parse_connection_string(connection_string: str) -> Dict[str, str]:
    """Parse connection string to extract components."""
    result = {}
    if not connection_string:
        return result
        
    pairs = connection_string.split(';')
    for pair in pairs:
        if '=' in pair:
            key, value = pair.split('=', 1)
            result[key] = value
    
    # Extract namespace from Endpoint
    if 'Endpoint' in result:
        endpoint = result['Endpoint']
        match = re.search(r'sb://([^/]+)\.servicebus\.windows\.net', endpoint)
        if match:
            result['namespace'] = match.group(1)
    
    return result

def validate_connection_string(connection_string: str, name: str) -> bool:
    """Validate connection string format and components."""
    logger.info(f"🔍 Validating {name} connection string...")
    
    if not connection_string:
        logger.error(f"❌ {name} connection string is empty or not set")
        return False
    
    # Parse connection string
    parts = parse_connection_string(connection_string)
    
    # Check required components
    required_parts = ['Endpoint', 'SharedAccessKeyName', 'SharedAccessKey']
    missing_parts = []
    
    for part in required_parts:
        if part not in parts:
            missing_parts.append(part)
    
    if missing_parts:
        logger.error(f"❌ {name} connection string missing: {', '.join(missing_parts)}")
        return False
    
    # Validate endpoint format
    endpoint = parts.get('Endpoint', '')
    if not endpoint.startswith('sb://') or not endpoint.endswith('.servicebus.windows.net/'):
        logger.error(f"❌ {name} endpoint format invalid: {endpoint}")
        return False
    
    # Log connection details (without sensitive info)
    logger.info(f"✅ {name} connection string format valid")
    logger.info(f"   • Namespace: {parts.get('namespace', 'unknown')}")
    logger.info(f"   • Key Name: {parts.get('SharedAccessKeyName', 'unknown')}")
    logger.info(f"   • Key Length: {len(parts.get('SharedAccessKey', ''))} characters")
    
    return True

def test_basic_connectivity(connection_string: str, name: str) -> bool:
    """Test basic connectivity to Service Bus."""
    logger.info(f"🔗 Testing {name} basic connectivity...")
    
    try:
        client = ServiceBusClient.from_connection_string(connection_string)
        
        # Try to create a receiver for a non-existent queue
        # This will test authentication without requiring an actual queue
        try:
            with client.get_queue_receiver("__diagnostic_test_queue__") as receiver:
                pass
        except Exception as e:
            error_msg = str(e).lower()
            
            # Expected error: queue not found (means auth worked)
            if "not found" in error_msg or "does not exist" in error_msg:
                logger.info(f"✅ {name} authentication successful")
                client.close()
                return True
            
            # Authentication errors
            if any(keyword in error_msg for keyword in ["auth", "token", "unauthorized", "forbidden"]):
                logger.error(f"❌ {name} authentication failed: {e}")
                client.close()
                return False
            
            # Network/connectivity errors
            if any(keyword in error_msg for keyword in ["timeout", "network", "connection"]):
                logger.error(f"❌ {name} network connectivity failed: {e}")
                client.close()
                return False
            
            # Other errors
            logger.warning(f"⚠️ {name} returned unexpected error: {e}")
            client.close()
            return False
            
    except Exception as e:
        logger.error(f"❌ {name} client creation failed: {e}")
        return False
    
    logger.info(f"✅ {name} connectivity test passed")
    return True

def test_queue_operations(connection_string: str, name: str, test_queue: str) -> bool:
    """Test actual queue operations if a test queue is available."""
    logger.info(f"🔧 Testing {name} queue operations with queue: {test_queue}")
    
    try:
        client = ServiceBusClient.from_connection_string(connection_string)
        
        # Test receiver creation
        try:
            with client.get_queue_receiver(test_queue) as receiver:
                logger.info(f"✅ {name} receiver created successfully")
                
                # Test peek operation
                try:
                    messages = receiver.peek_messages(max_message_count=1)
                    logger.info(f"✅ {name} peek operation successful (found {len(messages)} messages)")
                except Exception as e:
                    logger.warning(f"⚠️ {name} peek operation failed: {e}")
                
        except Exception as e:
            error_msg = str(e).lower()
            if "not found" in error_msg or "does not exist" in error_msg:
                logger.warning(f"⚠️ {name} queue '{test_queue}' does not exist")
                return False
            else:
                logger.error(f"❌ {name} receiver creation failed: {e}")
                return False
        
        # Test sender creation
        try:
            with client.get_queue_sender(test_queue) as sender:
                logger.info(f"✅ {name} sender created successfully")
        except Exception as e:
            logger.error(f"❌ {name} sender creation failed: {e}")
            return False
        
        client.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ {name} queue operations test failed: {e}")
        return False

def main():
    """Run comprehensive Service Bus diagnostics."""
    logger.info("🚀 Starting Azure Service Bus Connection Diagnostics")
    logger.info(f"⏰ Timestamp: {datetime.now()}")
    logger.info("=" * 60)
    
    # Check environment variables
    logger.info("📋 Checking environment variables...")
    
    if not SOURCE_CONNECTION_STR:
        logger.error("❌ SOURCE_SB_CONNECTION_STRING environment variable not set")
        logger.info("💡 Set it with: export SOURCE_SB_CONNECTION_STRING='your_connection_string'")
    
    if not DEST_CONNECTION_STR:
        logger.error("❌ DEST_SB_CONNECTION_STRING environment variable not set")
        logger.info("💡 Set it with: export DEST_SB_CONNECTION_STRING='your_connection_string'")
    
    if not SOURCE_CONNECTION_STR or not DEST_CONNECTION_STR:
        logger.error("❌ Missing required environment variables. Exiting.")
        return 1
    
    # Validate connection strings
    logger.info("\n" + "=" * 60)
    logger.info("🔍 STEP 1: Validating Connection Strings")
    logger.info("=" * 60)
    
    source_valid = validate_connection_string(SOURCE_CONNECTION_STR, "Source")
    dest_valid = validate_connection_string(DEST_CONNECTION_STR, "Destination")
    
    if not source_valid or not dest_valid:
        logger.error("❌ Connection string validation failed. Please fix the issues above.")
        return 1
    
    # Test basic connectivity
    logger.info("\n" + "=" * 60)
    logger.info("🔗 STEP 2: Testing Basic Connectivity")
    logger.info("=" * 60)
    
    source_connected = test_basic_connectivity(SOURCE_CONNECTION_STR, "Source")
    dest_connected = test_basic_connectivity(DEST_CONNECTION_STR, "Destination")
    
    if not source_connected or not dest_connected:
        logger.error("❌ Basic connectivity test failed.")
        logger.info("\n🔧 TROUBLESHOOTING TIPS:")
        logger.info("   • Verify your Shared Access Key is correct and not expired")
        logger.info("   • Check if the Service Bus namespace exists and is active")
        logger.info("   • Ensure your IP is not blocked (check Access Control in Azure portal)")
        logger.info("   • Verify network connectivity (firewalls, proxies)")
        logger.info("   • Try regenerating the Shared Access Key in Azure portal")
        return 1
    
    # Test with actual queues if available
    logger.info("\n" + "=" * 60)
    logger.info("🔧 STEP 3: Testing Queue Operations")
    logger.info("=" * 60)
    
    test_queues = ["core-user-persistence-dev_localtest"]  # Add your queue names here
    
    for queue_name in test_queues:
        logger.info(f"\n📦 Testing queue: {queue_name}")
        source_queue_ok = test_queue_operations(SOURCE_CONNECTION_STR, "Source", queue_name)
        dest_queue_ok = test_queue_operations(DEST_CONNECTION_STR, "Destination", queue_name)
        
        if source_queue_ok and dest_queue_ok:
            logger.info(f"✅ Queue '{queue_name}' is ready for duplication")
        else:
            logger.warning(f"⚠️ Queue '{queue_name}' has issues - check logs above")
    
    # Final summary
    logger.info("\n" + "=" * 60)
    logger.info("🎉 DIAGNOSTIC SUMMARY")
    logger.info("=" * 60)
    
    if source_connected and dest_connected:
        logger.info("✅ All basic connectivity tests passed!")
        logger.info("✅ Your Service Bus connections are working correctly")
        logger.info("💡 You can now run the main duplication script")
        return 0
    else:
        logger.error("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
